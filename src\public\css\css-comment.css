body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
textarea,
p,
blockquote,
th,
td,
input,
select,
textarea,
button {
  margin: 0;
  padding: 0;
}

/* 初始化标签在所有浏览器中的margin、padding值 */

fieldset,
img {
  border: 0 none;
}

/* 重置fieldset（表单分组）、图片的边框为0*/

dl,
ul,
ol,
menu,
li {
  list-style: none;
}

/* 重置类表前导符号为onne,menu在HTML5中有效 */

blockquote,
q {
  quotes: none;
}

/* 重置嵌套引用的引号类型 */

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

/* 重置嵌套引用*/

input,
select,
textarea,
button {
  vertical-align: middle;
}

/* 重置表单控件垂直居中*/

button {
  cursor: pointer;
  border: 0 none;
  background-color: transparent;
}

/* 重置表单button按钮效果 */

body {
  background: linear-gradient(180deg, #eaedf6 72%, #eeeaff 100%);
}

/* 重置body 页面背景为白色 */

body,
th,
td,
input,
select,
textarea,
button {
  color: #1c2024;
  font-size: 16px;
  line-height: 1;
  font-family: "Microsoft YaHei", "SimHei", "SimSun";
}

/* 重置页面文字属性 */

a {
  color: #1c2024;
  text-decoration: none;
  -webkit-tap-highlight-color: transparent;
}

/* 重置链接a标签 */

a:active,
a:hover {
  text-decoration: none;
}

/* 重置链接a标签的鼠标滑动效果 */

address,
caption,
cite,
code,
dfn,
em,
var {
  font-style: normal;
  font-weight: normal;
}

/* 重置样式标签的样式 */

caption {
  display: none;
}

/* 重置表格标题为隐藏 */

table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  table-layout: fixed;
}

/* 重置table属性 */

img {
  vertical-align: top;
}

/* 图片在当前行内的垂直位置 */

/* 页面设置 */

/* 取消a标签点击后的虚线框 */

a {
  outline: none;
}

a:active {
  star: expression(this.onFocus=this.blur());
}

/* 设置页面文字等在拖动鼠标选中情况下的背景色与文字颜色 */

/*
::selection {color: #fff;background-color: #4C6E78;}
::-moz-selection {color: #fff;background-color: #4C6E78;}
*/

/*清除浮动*/

.clear {
  clear: both;
}

.clear-float:after {
  display: block;
  clear: both;
  content: "";
}
.clear-float,
.clearfix {
  zoom: 1;
}
/*清除浮动--推荐使用*/

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
[v-cloak] {
  display: none;
}

/* 取消半透明灰色 */
html,
body {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
/* 修改chrome浏览器渲染黄色背景的时间 */
input:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s;
  color: #000 !important;
}

.no-spinners::-webkit-outer-spin-button,
.no-spinners::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinners {
  -moz-appearance: textfield; /* Firefox */
}

.drag {
  -webkit-app-region: drag;
}

.no-drag {
  -webkit-app-region: no-drag;
}
.my-scrollbar-y .el-scrollbar__wrap {
  overflow-x: hidden !important;
}
[v-cloak] {
  display: none;
}

.el-input--small {
  font-size: 14px !important;
}

.o-numberInput-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  width: 100%;
  height: 30px;
  overflow: hidden;
  color: #1c2024;
}

.o-numberInput-box:hover {
  border: 1px solid #3363ff;
}

.o-numberInput-box > i {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background: #f5f7fa;
  width: 30px;
  height: 100%;
  font-size: 13px;
}

.o-numberInput-box > .el-icon-minus {
  border-right: 1px solid #dcdfe6;
}
.o-numberInput-box > .el-icon-plus {
  border-left: 1px solid #dcdfe6;
}

.o-numberInput-box .el-input__inner {
  border: none;
}

.o-scrollbar::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.o-scrollbar::-webkit-scrollbar-thumb {
  transition: all 0.3s ease;
  border-radius: 5px;
  background: transparent;
  width: 8px;
}

.o-scrollbar:hover::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgb(210 215 229);
}

.o-scrollbar::-webkit-scrollbar-track {
  transition: all 0.3s ease;
  border-radius: 0;
  background: transparent;
}
.o-scrollbar:hover::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px #eee;
  background: #fff;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
}

.o-line {
  background: rgb(229 231 235);
  width: 100%;
  height: 1px;
}

.o-dot {
  flex-shrink: 0;
  border-radius: 50%;
  background: #3363ff;
  width: 6px;
  height: 6px;
}

.o-1vh {
  height: calc(100% - 20px);
}

.o-bg-primary-linear-short {
  background: linear-gradient(92deg, #3363ff 59.62%, #5f60e9 100%);
}

.o-bg-primary-linear-long {
  background: linear-gradient(92deg, #3363ff 23.41%, #775fdd 100%);
}

.o-bg-primary-light-animation {
  animation: primaryLightAnimation 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
  transition: none; /* 覆盖transition-all的影响 */
  will-change: background-size;
  background: linear-gradient(
    35deg,
    #ebeffc 36.68%,
    #f9faff 72.74%,
    #f9faff 94.1%
  );
  background-position: right top;
  background-size: 5000%;
}

.o-font-shadow {
  text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
}

.o-title-box-shadow {
  box-shadow: 0px 14px 31px -20px rgba(11, 49, 166, 0.15);
}

@keyframes primaryLightAnimation {
  0% {
    background-size: 5000%;
  }
  100% {
    background-size: 100%;
  }
}

.o-little-server-card-grid-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
}
.o-little-server-card-grid-box-long {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(230px, 1fr));
}

@property --o-service-card-title-bg-color-1 {
  syntax: "<color>";
  initial-value: #f8f8f8;
  inherits: false;
}
@property --o-service-card-title-bg-color-2 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}
@property --o-service-card-title-bg-color-3 {
  syntax: "<color>";
  initial-value: #eceffb;
  inherits: false;
}

.o-service-card-title-bg {
  transition:
    --o-service-card-title-bg-color-1 0.3s ease,
    --o-service-card-title-bg-color-2 0.3s ease,
    --o-service-card-title-bg-color-3 0.3s ease;
  background: linear-gradient(
    84deg,
    var(--o-service-card-title-bg-color-1) 0%,
    var(--o-service-card-title-bg-color-2) 49.81%,
    var(--o-service-card-title-bg-color-3) 99.63%
  );
}

.o-price-select-tag {
  display: flex;
  align-items: flex-end;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e5e7eb; /* border-gray-200 */
  border-radius: 0.25rem; /* rounded */
  overflow: hidden;
  color: #6b7280; /* text-gray-500 */
  font-size: 0.875rem; /* text-sm */
  line-height: 1.25rem;
}
.o-price-select-tag.disable {
  cursor: default;
}

.o-price-select-tag:not(.disable):hover {
  border-color: #3363ff; /* hover:border-primary */
  color: #3363ff; /* hover:text-primary */
}

.o-price-select-tag:not(.disable):active {
  transform: translateY(2px); /* y移动1px */
}

.o-price-select-tag > div:nth-child(1) {
  display: flex;
  align-items: flex-end;
  transition: all 0.3s ease;
  border-right: 1px solid #e5e7eb; /* border-r-gray-200 */
  background-color: #e5e7eb; /* bg-gray-200 */
  padding: 0.25rem 0.5rem; /* px-2 py-1 */
  line-height: 1;
}

.o-price-select-tag:not(.disable):hover > div:nth-child(1) {
  border-right-color: #3363ff; /* group-hover:border-r-primary */
  background-color: #3363ff; /* group-hover:bg-primary */
  color: white; /* group-hover:text-white */
}

.o-price-select-tag > div:nth-child(2) {
  display: flex;
  align-items: flex-end;
  padding: 0.25rem 0.75rem 0.25rem 0.5rem; /* py-1 pl-2 pr-3 */
  line-height: 1;
}

.o-service-card:hover {
  border-color: rgba(51, 99, 255, 0.5);
}

.o-service-card:hover:has(.el-icon-delete:hover) {
  box-shadow: 0 15px 20px -10px rgba(221, 62, 62, 0.25);
  border-color: #ef4444;
}

.o-service-card:hover:has(.el-icon-delete:hover) .o-service-card-title-bg {
  --o-service-card-title-bg-color-3: #fbecec;
}

/* 服务卡片列表动画 - Vue 2 语法 */
.o-service-card-list-enter-active,
.o-service-card-list-leave-active {
  transition: all 0.3s ease;
}

.o-service-card-list-move {
  transition: transform 0.3s ease;
}

/* 进入动画 */
.o-service-card-list-enter {
  transform: translateY(-30px) scale(0.9);
  opacity: 0;
}

/* 离开动画 - 以右上角为中心缩小 */
.o-service-card-list-leave-to {
  transform: scale(0);
  transform-origin: top right;
  opacity: 0;
}

.o-service-card-list-leave-active {
  position: absolute;
  transform-origin: top right;
  z-index: 0;
  width: calc(100% - 2rem);
}

/* 服务卡片悬停效果 */
.o-service-card {
  transition: all 0.2s ease;
}

.o-service-card:hover {
  transform: translateY(-2px);
}

/* 添加服务时的脉冲动画 */
@keyframes oServiceCardPulseAnimation {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.o-service-card-pulse {
  animation: oServiceCardPulseAnimation 0.6s ease-out;
}

/* 价格标签hover效果 */
.o-service-card:has(.o-price-select-tag:hover) .o-price-select-hover,
.o-service-card.price-tag-hovered .o-price-select-hover {
  border-color: #3363ff;
  background-color: rgba(51, 99, 255, 0.05);
}

/* 单价更新动画效果 */
.o-price-select-hover {
  transition: all 0.3s ease;
}

.o-price-select-hover.o-price-update-animation {
  animation: o-priceUpdatePulse-animation 0.6s ease-out;
}

@keyframes o-priceUpdatePulse-animation {
  0% {
    transform: scale(1);
    border-color: #3363ff;
    background-color: rgba(51, 99, 255, 0.1);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 3px rgba(51, 99, 255, 0.3);
    border-color: #3363ff;
    background-color: rgba(51, 99, 255, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: none;
    border-color: #3363ff;
    background-color: rgba(51, 99, 255, 0.05);
  }
}
/* 服务卡片高亮效果 */
.o-service-card-highlight {
  animation: o-serviceCardHighlight-animation 1s ease-out;
  box-shadow: 0 4px 12px rgba(51, 99, 255, 0.3) !important;
  border-color: #3363ff !important;
}

@keyframes o-serviceCardHighlight-animation {
  0% {
    transform: scale(1);
    background-color: rgba(51, 99, 255, 0.05);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 6px 16px rgba(51, 99, 255, 0.4);
    background-color: rgba(51, 99, 255, 0.1);
  }
  100% {
    transform: scale(1);
    background-color: rgba(51, 99, 255, 0.05);
  }
}

/* 弹出数字效果 */
.o-number-popup {
  position: fixed;
  transform: translate(20px, -14px);
  z-index: 9999;
  animation: o-numberPopup-animation 1s ease-out forwards;
  pointer-events: none;
  font-weight: bold;
  font-size: 18px;
}
.o-number-popup-add {
  position: fixed;
  transform: translate(-20px, -14px);
  z-index: 9999;
  animation: o-numberPopupAdd-animation 1s ease-out forwards;
  pointer-events: none;
  font-weight: bold;
  font-size: 18px;
}

/* 蓝色弹出数字（单击+1模式） */
.o-number-popup-blue {
  color: #3363ff;
}

/* 红色弹出数字（长按+10模式） */
.o-number-popup-purple {
  color: #7c3bff;
}

.o-number-popup-red {
  color: #dc2626;
}

@keyframes o-numberPopup-animation {
  0% {
    transform: translate(20px, -14px) scale(0.8);
    opacity: 1;
  }
  20% {
    transform: translate(25px, -20px) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(30px, -60px) scale(1);
    opacity: 0;
  }
}

@keyframes o-numberPopupAdd-animation {
  0% {
    transform: translate(-20px, -14px) scale(0.8);
    opacity: 1;
  }
  20% {
    transform: translate(-25px, -20px) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(-35px, -60px) scale(1);
    opacity: 0;
  }
}

/* 数量输入框动画 */
.o-numberInput-box i {
  transition: all 0.2s ease;
}

.o-numberInput-box i:hover {
  transform: scale(1.1);
  color: #3b82f6;
}

.o-numberInput-box i:active {
  transform: scale(0.95);
}

/* 价格输入框聚焦动画 */
.el-input__inner:focus {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

.el-input__inner:focus + .el-input__prefix {
  left: -1px;
}

/* 当有兄弟元素 .el-input-group__append 时，以右边中心为基点缩放 */
.el-input-group .el-input__inner:focus {
  transform: scale(1.02);
  transform-origin: right center;
  transition: transform 0.2s ease;
}

/* 删除时的特殊效果 - 以右上角为中心缩小变透明 */
.o-service-card-item-removing {
  transform-origin: top right;
  animation: oServiceCardItemRemoveAnimation 0.3s ease-in-out forwards;
}

@keyframes oServiceCardItemRemoveAnimation {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

.o-total-box {
  background: #f3f4f6;
}
.o-total-box::before {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 0;
  background: linear-gradient(180deg, #f3f4f6 0%, #fff 70%);
  width: 100%;
  height: 100px;
  content: "";
}

/* 搜索关键词高亮样式 */
.o-search-highlight {
  border-radius: 2px;
  background-color: yellow;
  padding: 1px 2px;
  font-weight: bold;
}

/* 删除按钮效果 - 合并重复规则 */
.el-icon-delete {
  position: relative;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 4px;
}

.el-icon-delete:hover {
  transform: scale(1.2);
  background-color: #fee2e2;
  color: #dc2626 !important;
}

.el-icon-delete:active {
  transform: scale(0.9);
  background-color: #fecaca;
}

.o-blur-mask {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  backdrop-filter: blur(9px);
  transition: opacity 0.2s ease;
  border-radius: inherit;
  background-color: rgb(255, 255, 255, 0.1);
  width: 100%;
  height: 100%;
}

/* 模糊遮罩显示动画 */
.o-blur-mask {
  animation: blur-mask-show 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
}

/* 模糊遮罩隐藏动画 */
.o-blur-mask.hide {
  animation: blur-mask-hide 0.4s cubic-bezier(0.6, -0.28, 0.735, 0.045) forwards;
}

@keyframes blur-mask-show {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(9px);
  }
}

@keyframes blur-mask-hide {
  0% {
    opacity: 1;
    backdrop-filter: blur(9px);
  }
  100% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
}

.o-tag-fuchsia {
  border: 1px solid #f5d0fe;
  background: #faf5ff;
  color: #d946ef;
}

.o-tag-pink {
  border: 1px solid #fbcfe8;
  background: #fdf2f8;
  color: #ec4899;
}

.o-tag-blue {
  border: 1px solid #bfdbfe;
  background: #eff6ff;
  color: #3b82f6;
}

.o-tag-indigo {
  border: 1px solid #c7d2fe;
  background: #eef2ff;
  color: #6366f1;
}

.o-tag-cyan {
  border: 1px solid #a5f3fc;
  background: #ecfeff;
  color: #06b6d4;
}

.o-tag-amber {
  border: 1px solid #fde68a;
  background: #fffbeb;
  color: #d97706;
}

.o-tag-lime {
  border: 1px solid #a3e635;
  background: #f7fee7;
  color: #4d7c0f;
}

.o-tag-gray {
  border: 1px solid #d1d5db;
  background: #f9fafb;
  color: #6b7280;
}

.o-tag-orange {
  border: 1px solid #fed7aa;
  background: #fff7ed;
  color: #f97316;
}

.o-tag-emerald {
  border: 1px solid #6ee7b7;
  background: #ecfdf5;
  color: #10b981;
}

.o-tag-rose {
  border: 1px solid #ffa1ad;
  background: #fff1f2;
  color: #ff2056;
}
